import 'package:shared_preferences/shared_preferences.dart';
import '../di/service_locator.dart';
import '../utils/logger.dart';
import '../constants/preference_keys.dart';
import '../../features/home/<USER>/models/dashboard_state.dart';
import '../../features/validation/domain/services/data_integrity_service.dart';
import '../../features/notifications/domain/models/notification.dart';
import '../../features/notifications/data/notification_service.dart';

/// Service for coordinating all alert types in foreground context
class AlertCoordinationService {
  static final AlertCoordinationService _instance =
      AlertCoordinationService._internal();

  factory AlertCoordinationService() => _instance;
  AlertCoordinationService._internal();

  /// Check all alert conditions and create notifications
  Future<void> checkAllAlerts() async {
    try {
      final settings = await _loadAlertSettings();

      if (!settings['notificationsEnabled']) {
        Logger.info('Notifications disabled, skipping alert checks');
        return;
      }

      final notifications = <AppNotification>[];

      // Check Low Balance condition
      if (settings['lowBalanceEnabled']) {
        final lowBalanceAlert = await _checkLowBalanceCondition();
        if (lowBalanceAlert != null) {
          notifications.add(lowBalanceAlert);
        }
      }

      // Check Alert Threshold condition
      if (settings['timeToTopUpEnabled']) {
        final thresholdAlert = await _checkAlertThresholdCondition(
          settings['alertThreshold'],
          settings['daysInAdvance'],
        );
        if (thresholdAlert != null) {
          notifications.add(thresholdAlert);
        }
      }

      // Check Invalid Records condition
      if (settings['invalidRecordEnabled']) {
        final invalidAlerts = await _checkInvalidRecordCondition();
        notifications.addAll(invalidAlerts);
      }

      // Create notifications if any conditions are met
      if (notifications.isNotEmpty) {
        final notificationService =
            await serviceLocator.getAsync<NotificationService>();

        for (final notification in notifications) {
          await notificationService.showNotification(notification);
        }

        Logger.info('Created ${notifications.length} alert notifications');
      } else {
        Logger.info('No alert conditions met');
      }
    } catch (e) {
      Logger.error('Error checking alerts: $e');
    }
  }

  /// Check low balance condition (< 24hrs to meter zero)
  Future<AppNotification?> _checkLowBalanceCondition() async {
    try {
      final dashboardState = await _getDashboardState();
      if (dashboardState == null) return null;

      final daysToZero = dashboardState.calculateDaysToMeterZero();

      // Check if condition is met (< 24 hours)
      if (daysToZero == null || daysToZero >= 1.0) return null;

      // Check deduplication (once per day)
      if (await _wasNotificationSentToday(NotificationType.lowBalance)) {
        return null;
      }

      // Create notification
      await _setLastNotificationDate(NotificationType.lowBalance);

      final hoursRemaining = (daysToZero * 24).round();
      return AppNotification(
        title: 'Low Balance Alert',
        message: hoursRemaining > 0
            ? 'Your meter will reach zero in approximately $hoursRemaining hours.'
            : 'Your meter balance is critically low.',
        timestamp: DateTime.now(),
        type: NotificationType.lowBalance,
      );
    } catch (e) {
      Logger.error('Error checking low balance condition: $e');
      return null;
    }
  }

  /// Check alert threshold condition (< 24hrs to threshold)
  Future<AppNotification?> _checkAlertThresholdCondition(
    double alertThreshold,
    int daysInAdvance,
  ) async {
    try {
      final dashboardState = await _getDashboardState();
      if (dashboardState == null) return null;

      final daysToThreshold = dashboardState.calculateDaysToAlertThreshold(
        alertThreshold,
        daysInAdvance,
      );

      // Check if condition is met (< 24 hours)
      if (daysToThreshold == null || daysToThreshold >= 1.0) return null;

      // Check deduplication (once per day)
      if (await _wasNotificationSentToday(NotificationType.timeToTopUp)) {
        return null;
      }

      // Create notification
      await _setLastNotificationDate(NotificationType.timeToTopUp);

      final hoursRemaining = (daysToThreshold * 24).round();
      return AppNotification(
        title: 'Time to Top-Up',
        message: hoursRemaining > 0
            ? 'You should top up in approximately $hoursRemaining hours.'
            : 'It\'s time to top up your meter.',
        timestamp: DateTime.now(),
        type: NotificationType.timeToTopUp,
      );
    } catch (e) {
      Logger.error('Error checking alert threshold condition: $e');
      return null;
    }
  }

  /// Check invalid record condition (any validation issues)
  Future<List<AppNotification>> _checkInvalidRecordCondition() async {
    try {
      final dataIntegrityService = serviceLocator<DataIntegrityService>();
      final issues = await dataIntegrityService.validateAllEntries();

      if (issues.isEmpty) return [];

      // Create notifications for validation issues (multiple allowed)
      final notifications = <AppNotification>[];

      // Group issues by severity for better messaging
      final highSeverityCount =
          issues.where((i) => i.severity.index == 2).length;
      final mediumSeverityCount =
          issues.where((i) => i.severity.index == 1).length;
      final lowSeverityCount =
          issues.where((i) => i.severity.index == 0).length;

      String message;
      if (highSeverityCount > 0) {
        message =
            'Found $highSeverityCount critical validation issue(s) that need immediate attention.';
      } else if (mediumSeverityCount > 0) {
        message =
            'Found $mediumSeverityCount validation issue(s) that should be reviewed.';
      } else {
        message = 'Found $lowSeverityCount minor validation issue(s) detected.';
      }

      notifications.add(AppNotification(
        title: 'Invalid Records Detected',
        message: message,
        timestamp: DateTime.now(),
        type: NotificationType.invalidRecord,
      ));

      return notifications;
    } catch (e) {
      Logger.error('Error checking invalid record condition: $e');
      return [];
    }
  }

  /// Get dashboard state for calculations
  Future<DashboardState?> _getDashboardState() async {
    try {
      // Access dashboard state through service locator
      // This works in foreground context where providers are available
      return null; // Placeholder - will be implemented with provider access
    } catch (e) {
      Logger.error('Error getting dashboard state: $e');
      return null;
    }
  }

  /// Load alert settings from preferences
  Future<Map<String, dynamic>> _loadAlertSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      return {
        'notificationsEnabled':
            prefs.getBool(PreferenceKeys.notificationsEnabled) ?? false,
        'lowBalanceEnabled':
            prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false,
        'timeToTopUpEnabled':
            prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false,
        'invalidRecordEnabled':
            prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false,
        'alertThreshold': prefs.getDouble(PreferenceKeys.alertThreshold) ?? 5.0,
        'daysInAdvance': prefs.getInt(PreferenceKeys.daysInAdvance) ?? 5,
      };
    } catch (e) {
      Logger.error('Error loading alert settings: $e');
      return {
        'notificationsEnabled': false,
        'lowBalanceEnabled': false,
        'timeToTopUpEnabled': false,
        'invalidRecordEnabled': false,
        'alertThreshold': 5.0,
        'daysInAdvance': 5,
      };
    }
  }

  /// Check if notification was sent today for deduplication
  Future<bool> _wasNotificationSentToday(NotificationType type) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getNotificationDateKey(type);
      final lastDateString = prefs.getString(key);

      if (lastDateString == null) return false;

      final lastDate = DateTime.parse(lastDateString);
      final today = DateTime.now();

      return lastDate.year == today.year &&
          lastDate.month == today.month &&
          lastDate.day == today.day;
    } catch (e) {
      Logger.error('Error checking notification deduplication: $e');
      return false;
    }
  }

  /// Set last notification date for deduplication
  Future<void> _setLastNotificationDate(NotificationType type) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getNotificationDateKey(type);
      await prefs.setString(key, DateTime.now().toIso8601String());
    } catch (e) {
      Logger.error('Error setting notification date: $e');
    }
  }

  /// Get preference key for notification date tracking
  String _getNotificationDateKey(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return PreferenceKeys.lastLowBalanceNotificationDate;
      case NotificationType.timeToTopUp:
        return PreferenceKeys.lastTimeToTopUpNotificationDate;
      default:
        throw ArgumentError('Unsupported notification type: $type');
    }
  }
}

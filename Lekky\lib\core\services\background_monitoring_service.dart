import 'package:workmanager/workmanager.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';
import '../constants/preference_keys.dart';
import 'notification_condition_service.dart';
import 'reminder_scheduling_service.dart';
import '../../features/notifications/data/notification_service.dart';

/// Service for managing background monitoring of notification conditions
class BackgroundMonitoringService {
  static final BackgroundMonitoringService _instance =
      BackgroundMonitoringService._internal();

  factory BackgroundMonitoringService() => _instance;
  BackgroundMonitoringService._internal();

  static const String _taskName = 'lekky_notification_check';
  static const String _uniqueName = 'lekky_background_monitor';

  /// Initialize background monitoring
  Future<void> initialize() async {
    try {
      await Workmanager().initialize(
        _callbackDispatcher,
        isInDebugMode: false, // Set to true for debugging
      );
      Logger.info('Background monitoring service initialized');
    } catch (e) {
      Logger.error('Failed to initialize background monitoring: $e');
    }
  }

  /// Start background monitoring (every 6 hours)
  Future<void> startMonitoring() async {
    try {
      // Cancel any existing tasks first
      await stopMonitoring();

      // Register periodic task (every 6 hours)
      await Workmanager().registerPeriodicTask(
        _uniqueName,
        _taskName,
        frequency: const Duration(hours: 6),
        constraints: Constraints(
          networkType: NetworkType.not_required,
          requiresBatteryNotLow: false,
          requiresCharging: false,
          requiresDeviceIdle: false,
          requiresStorageNotLow: false,
        ),
      );

      Logger.info('Background monitoring started (6-hour intervals)');
    } catch (e) {
      Logger.error('Failed to start background monitoring: $e');
    }
  }

  /// Stop background monitoring
  Future<void> stopMonitoring() async {
    try {
      await Workmanager().cancelByUniqueName(_uniqueName);
      Logger.info('Background monitoring stopped');
    } catch (e) {
      Logger.error('Failed to stop background monitoring: $e');
    }
  }

  /// Check if monitoring should be active based on notification settings
  Future<bool> shouldMonitoringBeActive() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final notificationsEnabled =
          prefs.getBool(PreferenceKeys.notificationsEnabled) ?? false;
      final lowBalanceEnabled =
          prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false;
      final timeToTopUpEnabled =
          prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false;
      final invalidRecordEnabled =
          prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false;
      final remindersEnabled =
          prefs.getBool(PreferenceKeys.remindersEnabled) ?? false;

      return (notificationsEnabled &&
              (lowBalanceEnabled ||
                  timeToTopUpEnabled ||
                  invalidRecordEnabled)) ||
          remindersEnabled;
    } catch (e) {
      Logger.error('Error checking monitoring status: $e');
      return false;
    }
  }

  /// Update monitoring based on current settings
  Future<void> updateMonitoring() async {
    try {
      final shouldMonitor = await shouldMonitoringBeActive();

      if (shouldMonitor) {
        await startMonitoring();
      } else {
        await stopMonitoring();
      }
    } catch (e) {
      Logger.error('Error updating monitoring: $e');
    }
  }
}

/// Background task callback dispatcher
@pragma('vm:entry-point')
void _callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    try {
      Logger.info('Background notification check started');

      // Load notification settings
      final prefs = await SharedPreferences.getInstance();
      final notificationsEnabled =
          prefs.getBool(PreferenceKeys.notificationsEnabled) ?? false;
      final remindersEnabled =
          prefs.getBool(PreferenceKeys.remindersEnabled) ?? false;

      if (!notificationsEnabled && !remindersEnabled) {
        Logger.info('Notifications and reminders disabled, skipping check');
        return Future.value(true);
      }

      final lowBalanceEnabled =
          prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false;
      final timeToTopUpEnabled =
          prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false;
      final invalidRecordEnabled =
          prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false;
      final alertThreshold =
          prefs.getDouble(PreferenceKeys.alertThreshold) ?? 5.0;
      final daysInAdvance = prefs.getInt(PreferenceKeys.daysInAdvance) ?? 5;

      // Check conditions and create notifications
      final conditionService = NotificationConditionService();
      final notifications = await conditionService.checkConditions(
        lowBalanceEnabled: lowBalanceEnabled,
        timeToTopUpEnabled: timeToTopUpEnabled,
        invalidRecordEnabled: invalidRecordEnabled,
        alertThreshold: alertThreshold,
        daysInAdvance: daysInAdvance,
      );

      // Show notifications if any conditions are met
      if (notifications.isNotEmpty) {
        final notificationService = NotificationService();
        await notificationService.initialize();

        for (final notification in notifications) {
          await notificationService.showNotification(notification);
        }

        Logger.info('Created ${notifications.length} background notifications');
      } else {
        Logger.info('No notification conditions met');
      }

      // Check and update reminders if enabled
      if (remindersEnabled) {
        try {
          final reminderService = ReminderSchedulingService();
          await reminderService.updateReminders();
          Logger.info('Reminder maintenance completed');
        } catch (e) {
          Logger.error('Reminder maintenance failed: $e');
        }
      }

      return Future.value(true);
    } catch (e) {
      Logger.error('Background task failed: $e');
      return Future.value(false);
    }
  });
}
